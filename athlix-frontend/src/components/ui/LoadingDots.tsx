import React from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';

interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({ 
  size = 'md', 
  color = 'bg-gray-500' 
}) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  return (
    <div className="flex space-x-1 items-center">
      <div 
        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}
        style={{ animationDelay: '0ms' }}
      ></div>
      <div 
        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}
        style={{ animationDelay: '150ms' }}
      ></div>
      <div 
        className={`${sizeClasses[size]} ${color} rounded-full animate-bounce`}
        style={{ animationDelay: '300ms' }}
      ></div>
    </div>
  );
};

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg max-w-xs">
      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
        <SparklesIcon className="w-4 h-4 text-white" />
      </div>
      <div className="flex flex-col">
        <span className="text-xs text-gray-500 mb-1">ATHLIX AI is typing...</span>
        <LoadingDots size="sm" color="bg-gray-400" />
      </div>
    </div>
  );
};

export default LoadingDots;
