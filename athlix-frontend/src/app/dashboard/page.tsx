'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import { 
  HeartIcon, 
  ChatBubbleLeftIcon, 
  ClipboardDocumentListIcon,
  UserIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { formatDate } from '@/lib/utils';
import { systemAPI, chatAPI } from '@/lib/api';

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuth();
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [recentChats, setRecentChats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    const fetchDashboardData = async () => {
      try {
        // Fetch health status
        const health = await systemAPI.getHealth();
        setHealthStatus(health);

        // Fetch recent chat sessions
        const chats = await chatAPI.getSessions();
        setRecentChats(chats.sessions?.slice(0, 3) || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isAuthenticated]);

  const quickActions = [
    {
      title: 'Start New Chat',
      description: 'Get instant health advice from our AI assistant',
      icon: ChatBubbleLeftIcon,
      href: '/chat',
      color: 'bg-blue-500',
    },
    {
      title: 'Treatment Assessment',
      description: 'Upload images and get personalized treatment plans',
      icon: ClipboardDocumentListIcon,
      href: '/treatment',
      color: 'bg-green-500',
    },
    {
      title: 'Health Profile',
      description: 'Manage your health information and preferences',
      icon: UserIcon,
      href: '/profile',
      color: 'bg-purple-500',
    },
  ];

  const stats = [
    {
      name: 'Total Users',
      value: healthStatus?.users_count || 0,
      icon: UserIcon,
      change: '+12%',
      changeType: 'positive',
    },
    {
      name: 'Active Chats',
      value: healthStatus?.active_chats || 0,
      icon: ChatBubbleLeftIcon,
      change: '+5%',
      changeType: 'positive',
    },
    {
      name: 'Treatment Sessions',
      value: healthStatus?.treatment_sessions || 0,
      icon: ClipboardDocumentListIcon,
      change: '+8%',
      changeType: 'positive',
    },
  ];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.first_name}!
          </h1>
          <p className="mt-2 text-gray-600">
            Here's your health dashboard overview. How can ATHLIX help you today?
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {stats.map((stat) => (
            <Card key={stat.name}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <stat.icon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">{stat.name}</p>
                    <div className="flex items-baseline">
                      <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                      <p className={`ml-2 text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {quickActions.map((action) => (
              <Link key={action.title} href={action.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 p-3 rounded-lg ${action.color}`}>
                        <action.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="ml-4">
                        <h3 className="text-lg font-medium text-gray-900">{action.title}</h3>
                        <p className="text-sm text-gray-500">{action.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Chats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ChatBubbleLeftIcon className="h-5 w-5 mr-2" />
                Recent Chats
              </CardTitle>
              <CardDescription>Your latest conversations with ATHLIX</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : recentChats.length > 0 ? (
                <div className="space-y-4">
                  {recentChats.map((chat) => (
                    <div key={chat.session_id} className="border-b border-gray-200 pb-3 last:border-b-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            Chat Session
                          </p>
                          <p className="text-xs text-gray-500">
                            {chat.message_count} messages
                          </p>
                        </div>
                        <div className="text-xs text-gray-400">
                          {chat.last_activity ? formatDate(chat.last_activity) : 'No activity'}
                        </div>
                      </div>
                    </div>
                  ))}
                  <Link href="/chat">
                    <Button variant="outline" size="sm" className="w-full">
                      View All Chats
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="text-center py-6">
                  <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No chats yet</h3>
                  <p className="mt-1 text-sm text-gray-500">Start your first conversation with ATHLIX</p>
                  <div className="mt-6">
                    <Link href="/chat">
                      <Button size="sm">Start Chat</Button>
                    </Link>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Health Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <HeartIcon className="h-5 w-5 mr-2" />
                Health Tips
              </CardTitle>
              <CardDescription>Daily recommendations for better health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-900">Stay Hydrated</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Drink at least 8 glasses of water daily to maintain optimal health.
                  </p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900">Regular Exercise</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Aim for 30 minutes of moderate exercise 5 times a week.
                  </p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="text-sm font-medium text-purple-900">Quality Sleep</h4>
                  <p className="text-sm text-purple-700 mt-1">
                    Get 7-9 hours of quality sleep each night for optimal recovery.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
