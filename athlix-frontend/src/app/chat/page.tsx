'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import {
  PaperAirplaneIcon,
  ComputerDesktopIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { chatAPI } from '@/lib/api';
import { formatTime, getInitials } from '@/lib/utils';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import toast from 'react-hot-toast';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export default function ChatPage() {
  const { user, isAuthenticated } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [chatSessions, setChatSessions] = useState<Array<{
    session_id: string;
    message_count: number;
    last_activity?: string;
  }>>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showingDeletedContent, setShowingDeletedContent] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }

    fetchChatSessions();
  }, [isAuthenticated]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchChatSessions = async () => {
    try {
      const response = await chatAPI.getSessions();
      setChatSessions(response.sessions || []);
    } catch (error) {
      console.error('Error fetching chat sessions:', error);
    }
  };

  const createNewChat = async () => {
    try {
      const response = await chatAPI.createSession();
      setCurrentSessionId(response.session_id);
      setMessages([]);
      await fetchChatSessions();
      toast.success('New chat session created');
    } catch (error) {
      console.error('Error creating chat session:', error);
      toast.error('Failed to create new chat session');
    }
  };

  const loadChatHistory = async (sessionId: string) => {
    try {
      const response = await chatAPI.getChatHistory(sessionId);
      setCurrentSessionId(sessionId);
      
      const formattedMessages = response.messages.map((msg: {
        message_id: string;
        role: string;
        content: string;
        created_at: string;
      }) => ({
        id: msg.message_id,
        role: msg.role,
        content: msg.content,
        timestamp: msg.created_at,
      }));
      
      setMessages(formattedMessages);
    } catch (error) {
      console.error('Error loading chat history:', error);
      toast.error('Failed to load chat history');
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);

    try {
      let sessionId = currentSessionId;
      
      // Create new session if none exists
      if (!sessionId) {
        const newSession = await chatAPI.createSession();
        sessionId = newSession.session_id;
        setCurrentSessionId(sessionId);
        await fetchChatSessions();
      }

      // Send message to backend
      const response = await chatAPI.sendMessage({
        session_id: sessionId || undefined,
        message: inputMessage,
        message_type: 'text',
      });

      // Add AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
      
      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleDeleteClick = (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent loading the chat when clicking delete
    setSessionToDelete(sessionId);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!sessionToDelete) return;

    setDeleteLoading(true);

    try {
      // If deleting the current session, show content briefly before clearing
      if (sessionToDelete === currentSessionId && messages.length > 0) {
        setShowingDeletedContent(true);

        // Wait 1.5 seconds to show the content
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      // Delete the session
      await chatAPI.deleteSession(sessionToDelete);

      // Update the sessions list
      const updatedSessions = chatSessions.filter(
        session => session.session_id !== sessionToDelete
      );
      setChatSessions(updatedSessions);

      // Handle current session logic
      if (sessionToDelete === currentSessionId) {
        // Clear messages and reset current session
        setMessages([]);
        setShowingDeletedContent(false);

        // If there are remaining sessions, load the most recent one
        if (updatedSessions.length > 0) {
          const mostRecentSession = updatedSessions[0];
          await loadChatHistory(mostRecentSession.session_id);
        } else {
          // No sessions left, reset to empty state
          setCurrentSessionId(null);
        }
      }

      toast.success('Chat deleted successfully');
    } catch (error) {
      console.error('Error deleting chat session:', error);
      toast.error('Failed to delete chat session');
    } finally {
      setDeleteLoading(false);
      setDeleteConfirmOpen(false);
      setSessionToDelete(null);
      setShowingDeletedContent(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setSessionToDelete(null);
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]">
          {/* Chat Sessions Sidebar */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold">Chat Sessions</h2>
                  <Button size="sm" onClick={createNewChat}>
                    <PlusIcon className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  {chatSessions.map((session) => (
                    <div
                      key={session.session_id}
                      className={`group relative p-3 rounded-lg cursor-pointer transition-colors border ${
                        currentSessionId === session.session_id
                          ? 'bg-blue-50 border-blue-200 text-blue-900'
                          : 'hover:bg-accent hover:text-accent-foreground border-transparent'
                      }`}
                    >
                      <div
                        onClick={() => loadChatHistory(session.session_id)}
                        className="flex-1"
                      >
                        <div className="text-sm font-medium text-card-foreground pr-8">Chat Session</div>
                        <div className="text-xs text-muted-foreground">
                          {session.message_count} messages
                        </div>
                        {session.last_activity && (
                          <div className="text-xs text-muted-foreground">
                            {formatTime(session.last_activity)}
                          </div>
                        )}
                      </div>

                      {/* Delete button */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6 text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                          onClick={(e) => handleDeleteClick(session.session_id, e)}
                          title="Delete chat session"
                        >
                          <TrashIcon className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  {chatSessions.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <p className="text-sm">No chat sessions yet</p>
                      <Button size="sm" onClick={createNewChat} className="mt-2">
                        Start Your First Chat
                      </Button>
                    </div>
                  )}

                  {chatSessions.length === 1 && (
                    <div className="text-xs text-muted-foreground text-center mt-4 px-2">
                      <p>💡 Tip: Hover over a chat to see the delete option</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Area */}
          <div className="lg:col-span-3">
            <Card className="h-full flex flex-col">
              {/* Chat Header */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center justify-between">
                  <h1 className="text-xl font-semibold text-card-foreground">ATHLIX AI Assistant</h1>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Online</span>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {showingDeletedContent && (
                  <div className="text-center py-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      Deleting this conversation...
                    </p>
                  </div>
                )}

                {messages.length === 0 ? (
                  <div className="text-center py-12">
                    <ComputerDesktopIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-medium text-card-foreground">Start a conversation</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      Ask ATHLIX about your health, injuries, or treatment options.
                    </p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`flex max-w-xs lg:max-w-md ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                        <div className={`flex-shrink-0 ${message.role === 'user' ? 'ml-2' : 'mr-2'}`}>
                          {message.role === 'user' ? (
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-white">
                                {user ? getInitials(user.first_name, user.last_name) : 'U'}
                              </span>
                            </div>
                          ) : (
                            <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                              <ComputerDesktopIcon className="w-4 h-4 text-white" />
                            </div>
                          )}
                        </div>
                        <div
                          className={`px-4 py-2 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted text-muted-foreground'
                          }`}
                        >
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                          <p className={`text-xs mt-1 opacity-70`}>
                            {formatTime(message.timestamp)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                
                {loading && (
                  <div className="flex justify-start">
                    <div className="flex mr-2">
                      <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
                        <ComputerDesktopIcon className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="bg-muted px-4 py-2 rounded-lg">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="p-4 border-t border-border">
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Input
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      disabled={loading}
                    />
                  </div>
                  <Button
                    onClick={sendMessage}
                    disabled={!inputMessage.trim() || loading}
                    size="icon"
                  >
                    <PaperAirplaneIcon className="w-4 h-4" />
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Press Enter to send, Shift+Enter for new line
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirmOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Chat Session"
        message={
          sessionToDelete === currentSessionId
            ? "Are you sure you want to delete this chat session? You're currently viewing this conversation and it will be cleared. This action cannot be undone."
            : "Are you sure you want to delete this chat session? This action cannot be undone."
        }
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        loading={deleteLoading}
      />
    </div>
  );
}
