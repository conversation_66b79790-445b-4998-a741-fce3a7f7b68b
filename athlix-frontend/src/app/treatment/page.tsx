'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/layout/Navbar';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import {
  ClipboardDocumentListIcon,
  PhotoIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  CloudArrowUpIcon,
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  PlusIcon,
  UserIcon,
  SparklesIcon,
  ClockIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';
import { treatmentAPI, chatAPI } from '@/lib/api';
import toast from 'react-hot-toast';
import { LoadingDots, TypingIndicator } from '@/components/ui/LoadingDots';

interface TreatmentSession {
  session_id: string;
  status: 'image_upload' | 'conversation' | 'completed';
  ai_response?: string;
  conversation_history?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
  message?: string;
}

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

// ChatGPT-style History Item Component
const ChatHistoryItem = ({ session, onLoad }: {
  session: { session_id: string; title: string; last_activity: string; type: 'treatment' | 'chat' };
  onLoad: (sessionId: string, type: 'treatment' | 'chat') => void;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Generate a more descriptive title
  const getDisplayTitle = () => {
    if (session.type === 'treatment') {
      return `Medical Analysis`;
    } else {
      return `Health Chat`;
    }
  };

  // Generate unique chat ID based on timestamp and type
  const getChatId = () => {
    const date = new Date(session.last_activity);
    const timeStr = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    return `${session.type === 'treatment' ? '🏥' : '💬'} ${getDisplayTitle()} ${timeStr}`;
  };

  return (
    <button
      className="w-full text-left p-2 rounded-lg hover:bg-gray-700 transition-colors group relative"
      onClick={() => onLoad(session.session_id, session.type)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center space-x-3">
        <div className={`w-6 h-6 rounded flex items-center justify-center flex-shrink-0 ${
          session.type === 'treatment' ? 'bg-green-600' : 'bg-blue-600'
        }`}>
          {session.type === 'treatment' ? (
            <PhotoIcon className="w-3 h-3 text-white" />
          ) : (
            <ChatBubbleLeftRightIcon className="w-3 h-3 text-white" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm text-gray-200 truncate group-hover:text-white transition-colors">
            {getChatId()}
          </p>
          <p className="text-xs text-gray-500 group-hover:text-gray-400 transition-colors">
            {new Date(session.last_activity).toLocaleDateString()}
          </p>
        </div>
        {isHovered && (
          <div className="flex-shrink-0">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </div>
        )}
      </div>
    </button>
  );
};

export default function TreatmentPage() {
  const { isAuthenticated } = useAuth();
  const [treatmentSession, setTreatmentSession] = useState<TreatmentSession | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'start' | 'image_upload' | 'conversation' | 'chat'>('start');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [chatSessionId, setChatSessionId] = useState<string | null>(null);
  const [treatmentInput, setTreatmentInput] = useState('');
  const [treatmentMessages, setTreatmentMessages] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>>([]);
  const [chatHistory, setChatHistory] = useState<Array<{
    session_id: string;
    title: string;
    last_activity: string;
    type: 'treatment' | 'chat';
  }>>([]);
  const [showHistory, setShowHistory] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
  }, [isAuthenticated]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [treatmentMessages, chatMessages, loading]);

  // Load chat history on component mount
  useEffect(() => {
    loadChatHistory();
  }, []);

  const loadChatHistory = async () => {
    try {
      const [chatSessions, treatmentSessions] = await Promise.all([
        chatAPI.getSessions(),
        // We'll need to add a treatment sessions endpoint
        fetch('http://localhost:8002/treatment/sessions', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        }).then(res => res.ok ? res.json() : [])
      ]);

      const history = [
        ...chatSessions.map((session: any) => ({
          session_id: session.session_id,
          title: session.title || 'Chat Session',
          last_activity: session.last_activity || session.created_at,
          type: 'chat' as const
        })),
        ...treatmentSessions.map((session: any) => ({
          session_id: session.session_id,
          title: session.title || 'Treatment Session',
          last_activity: session.last_activity || session.created_at,
          type: 'treatment' as const
        }))
      ].sort((a, b) => new Date(b.last_activity).getTime() - new Date(a.last_activity).getTime());

      setChatHistory(history);
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  const loadSession = async (sessionId: string, type: 'treatment' | 'chat') => {
    try {
      setLoading(true);

      if (type === 'treatment') {
        // Load treatment session
        const response = await fetch(`http://localhost:8002/treatment/${sessionId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        });

        if (response.ok) {
          const sessionData = await response.json();
          setTreatmentSession(sessionData);

          // Load conversation history
          if (sessionData.conversation_history) {
            const messages = sessionData.conversation_history.map((msg: any) => ({
              ...msg,
              timestamp: msg.timestamp || new Date().toISOString()
            }));
            setTreatmentMessages(messages);
          }

          setStep('conversation');
        }
      } else {
        // Load chat session
        setChatSessionId(sessionId);

        // Load chat history
        const history = await chatAPI.getChatHistory(sessionId);
        const messages = history.map((msg: any) => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp
        }));
        setChatMessages(messages);

        setStep('chat');
      }

      setShowHistory(false); // Close sidebar after loading
      toast.success('Session loaded successfully');
    } catch (error) {
      console.error('Error loading session:', error);
      toast.error('Failed to load session');
    } finally {
      setLoading(false);
    }
  };

  const startTreatmentSession = async () => {
    setLoading(true);
    try {
      const response = await treatmentAPI.startSession();
      setTreatmentSession(response);
      setStep('image_upload');
      toast.success('Treatment session started - please upload an image');
    } catch (error) {
      console.error('Error starting treatment session:', error);
      toast.error('Failed to start treatment session');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !treatmentSession) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    setLoading(true);
    try {
      // Convert to base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;
        setUploadedImage(base64);

        try {
          const data = await treatmentAPI.uploadImage({
            session_id: treatmentSession.session_id,
            image_data: base64,
          });

          setTreatmentSession(data);

          // Initialize conversation with AI response
          if (data.conversation_history && data.conversation_history.length > 0) {
            const messages = data.conversation_history.map((msg: any) => ({
              ...msg,
              timestamp: new Date().toISOString()
            }));
            setTreatmentMessages(messages);
          } else if (data.ai_response) {
            // Fallback: if no conversation_history but ai_response exists
            const aiMessage = {
              role: 'assistant' as const,
              content: data.ai_response,
              timestamp: new Date().toISOString()
            };
            setTreatmentMessages([aiMessage]);
          }

          setStep('conversation');
          toast.success('Image uploaded and analyzed! You can now chat with our AI assistant.');
        } catch (error) {
          console.error('Error uploading image:', error);
          toast.error('Failed to upload image');
        } finally {
          setLoading(false);
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      toast.error('Failed to process image');
      setLoading(false);
    }
  };

  const startDirectChat = async () => {
    setLoading(true);
    try {
      const response = await chatAPI.createSession();
      setChatSessionId(response.session_id);
      setStep('chat');
      toast.success('Chat session started');
    } catch (error) {
      console.error('Error starting chat session:', error);
      toast.error('Failed to start chat session');
    } finally {
      setLoading(false);
    }
  };

  const sendTreatmentMessage = async () => {
    if (!treatmentSession || !treatmentInput.trim()) return;

    const userMessage = {
      role: 'user' as const,
      content: treatmentInput,
      timestamp: new Date().toISOString(),
    };

    setTreatmentMessages(prev => [...prev, userMessage]);
    const currentInput = treatmentInput;
    setTreatmentInput('');
    setLoading(true);

    try {
      const response = await treatmentAPI.sendMessage({
        session_id: treatmentSession.session_id,
        message: currentInput,
      });

      const aiMessage = {
        role: 'assistant' as const,
        content: response.ai_response,
        timestamp: new Date().toISOString(),
      };

      setTreatmentMessages(prev => [...prev, aiMessage]);

      // Update session with latest conversation
      setTreatmentSession(prev => prev ? {
        ...prev,
        conversation_history: response.conversation_history
      } : null);

    } catch (error) {
      console.error('Error sending treatment message:', error);
      toast.error('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const sendChatMessage = async () => {
    if (!chatInput.trim() || !chatSessionId) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: chatInput,
      timestamp: new Date().toISOString(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    const currentInput = chatInput;
    setChatInput('');
    setLoading(true);

    try {
      const response = await chatAPI.sendMessage({
        session_id: chatSessionId,
        message: currentInput,
      });

      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: response.response,
        timestamp: response.timestamp,
      };

      setChatMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending chat message:', error);
      toast.error('Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  const resetSession = () => {
    setTreatmentSession(null);
    setCurrentAnswer('');
    setUploadedImage(null);
    setChatMessages([]);
    setChatInput('');
    setChatSessionId(null);
    setTreatmentInput('');
    setTreatmentMessages([]);
    setStep('start');
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white flex">
      {/* ChatGPT-Style History Sidebar */}
      {showHistory && (
        <>
          {/* Mobile Overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={() => setShowHistory(false)}
          />

          {/* Sidebar */}
          <div className="fixed md:relative w-80 bg-gray-900 text-white flex flex-col h-screen z-50">
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">Chat History</h2>
              <button
                onClick={() => setShowHistory(false)}
                className="p-1 hover:bg-gray-700 rounded"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <Button
              onClick={resetSession}
              className="w-full mt-3 bg-transparent border border-gray-600 hover:bg-gray-700 text-white"
              size="sm"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          </div>

          {/* Chat Sessions List */}
          <div className="flex-1 overflow-y-auto p-2">
            {chatHistory.length === 0 ? (
              <div className="text-center py-8">
                <ClockIcon className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400 text-sm">No conversations yet</p>
                <p className="text-gray-500 text-xs mt-1">Start a new chat to see your history</p>
              </div>
            ) : (
              <div className="space-y-1">
                {/* Group by date */}
                {(() => {
                  const today = new Date();
                  const yesterday = new Date(today);
                  yesterday.setDate(yesterday.getDate() - 1);
                  const lastWeek = new Date(today);
                  lastWeek.setDate(lastWeek.getDate() - 7);

                  const todaySessions = chatHistory.filter(session =>
                    new Date(session.last_activity).toDateString() === today.toDateString()
                  );
                  const yesterdaySessions = chatHistory.filter(session =>
                    new Date(session.last_activity).toDateString() === yesterday.toDateString()
                  );
                  const lastWeekSessions = chatHistory.filter(session => {
                    const sessionDate = new Date(session.last_activity);
                    return sessionDate < yesterday && sessionDate >= lastWeek;
                  });
                  const olderSessions = chatHistory.filter(session =>
                    new Date(session.last_activity) < lastWeek
                  );

                  return (
                    <>
                      {todaySessions.length > 0 && (
                        <div>
                          <h3 className="text-xs font-medium text-gray-400 px-2 py-2 uppercase tracking-wider">Today</h3>
                          {todaySessions.map((session) => (
                            <ChatHistoryItem key={session.session_id} session={session} onLoad={loadSession} />
                          ))}
                        </div>
                      )}

                      {yesterdaySessions.length > 0 && (
                        <div>
                          <h3 className="text-xs font-medium text-gray-400 px-2 py-2 uppercase tracking-wider">Yesterday</h3>
                          {yesterdaySessions.map((session) => (
                            <ChatHistoryItem key={session.session_id} session={session} onLoad={loadSession} />
                          ))}
                        </div>
                      )}

                      {lastWeekSessions.length > 0 && (
                        <div>
                          <h3 className="text-xs font-medium text-gray-400 px-2 py-2 uppercase tracking-wider">Previous 7 days</h3>
                          {lastWeekSessions.map((session) => (
                            <ChatHistoryItem key={session.session_id} session={session} onLoad={loadSession} />
                          ))}
                        </div>
                      )}

                      {olderSessions.length > 0 && (
                        <div>
                          <h3 className="text-xs font-medium text-gray-400 px-2 py-2 uppercase tracking-wider">Older</h3>
                          {olderSessions.map((session) => (
                            <ChatHistoryItem key={session.session_id} session={session} onLoad={loadSession} />
                          ))}
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            )}
          </div>
          </div>
        </>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* ChatGPT-style Header */}
        <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <SparklesIcon className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">ATHLIX AI</h1>
              <p className="text-xs text-gray-500">Medical Assistant</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setShowHistory(!showHistory)}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <ClockIcon className="w-4 h-4" />
              <span>History</span>
            </Button>
            <Button
              onClick={resetSession}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <PlusIcon className="w-4 h-4" />
              <span>New Chat</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col max-w-4xl mx-auto w-full">{step === 'start' && (
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center max-w-2xl">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <SparklesIcon className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Welcome to ATHLIX AI
            </h2>
            <p className="text-gray-600 mb-8">
              Your AI medical assistant. Upload an image for analysis or start chatting directly.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <button
                onClick={startTreatmentSession}
                disabled={loading}
                className="p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all group"
              >
                <PhotoIcon className="w-8 h-8 text-green-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
                <h3 className="font-medium text-gray-900 mb-2">Image Analysis</h3>
                <p className="text-sm text-gray-600">Upload a photo for AI-powered medical analysis</p>
              </button>

              <button
                onClick={startDirectChat}
                disabled={loading}
                className="p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50 transition-all group"
              >
                <ChatBubbleLeftRightIcon className="w-8 h-8 text-blue-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
                <h3 className="font-medium text-gray-900 mb-2">Direct Chat</h3>
                <p className="text-sm text-gray-600">Chat directly with our AI assistant</p>
              </button>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-left">
              <div className="flex">
                <svg className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">Medical Disclaimer</h3>
                  <p className="mt-1 text-sm text-yellow-700">
                    This AI assistant provides information for educational purposes only and should not replace professional medical advice.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

        {step === 'image_upload' && treatmentSession && (
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="max-w-md w-full">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <PhotoIcon className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Upload Medical Image</h2>
                <p className="text-gray-600">Upload a clear image for AI analysis</p>
              </div>

              <div
                className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors cursor-pointer upload-area"
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                {loading ? (
                  <div className="space-y-4">
                    <div className="w-12 h-12 mx-auto">
                      <LoadingDots size="lg" color="bg-blue-500" />
                    </div>
                    <p className="text-gray-600">Analyzing image...</p>
                  </div>
                ) : uploadedImage ? (
                  <div className="space-y-4">
                    <img
                      src={uploadedImage}
                      alt="Uploaded"
                      className="max-w-full max-h-48 mx-auto rounded-lg"
                    />
                    <p className="text-green-600 font-medium">✓ Image uploaded successfully!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div>
                      <p className="text-lg font-medium text-gray-900">Drop your image here</p>
                      <p className="text-gray-500">or click to browse</p>
                      <p className="text-xs text-gray-400 mt-2">PNG, JPG, GIF up to 5MB</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {(step === 'conversation' || step === 'chat') && (
          <>
            {/* Chat Messages Area */}
            <div className="flex-1 overflow-y-auto px-4 py-6">
              <div className="max-w-3xl mx-auto space-y-6">
                {/* Show uploaded image if in treatment conversation */}
                {step === 'conversation' && uploadedImage && (
                  <div className="flex justify-center">
                    <div className="bg-gray-50 rounded-xl p-4 max-w-sm">
                      <img
                        src={uploadedImage}
                        alt="Uploaded medical image"
                        className="w-full rounded-lg"
                      />
                      <p className="text-xs text-gray-500 mt-2 text-center">Uploaded for analysis</p>
                    </div>
                  </div>
                )}



                {/* Messages */}
                {(step === 'conversation' ? treatmentMessages : chatMessages).length === 0 && !loading ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <SparklesIcon className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {step === 'conversation' ? 'AI is analyzing your image...' : 'Start a conversation'}
                    </h3>
                    <p className="text-gray-500">
                      {step === 'conversation'
                        ? 'Please wait while our AI analyzes your medical image'
                        : 'Ask me anything about your health and wellness'
                      }
                    </p>
                  </div>
                ) : (
                  <>
                    {(step === 'conversation' ? treatmentMessages : chatMessages).map((message, index) => (
                      <div key={index} className="flex items-start space-x-3 message-enter">
                        {/* Avatar */}
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 avatar ${
                          message.role === 'user'
                            ? 'bg-blue-500'
                            : 'bg-green-500'
                        }`}>
                          {message.role === 'user' ? (
                            <UserIcon className="w-5 h-5 text-white" />
                          ) : (
                            <SparklesIcon className="w-5 h-5 text-white" />
                          )}
                        </div>

                        {/* Message Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-sm font-medium text-gray-900">
                              {message.role === 'user' ? 'You' : 'ATHLIX AI'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <div className="prose prose-sm max-w-none">
                            <p className="text-gray-800 whitespace-pre-wrap leading-relaxed">
                              {message.content}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Typing Indicator */}
                    {loading && (
                      <div className="flex items-start space-x-3 typing-indicator">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <SparklesIcon className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="text-sm font-medium text-gray-900">ATHLIX AI</span>
                            <span className="text-xs text-gray-500">typing...</span>
                          </div>
                          <div className="bg-gray-100 rounded-2xl px-4 py-3 inline-block">
                            <LoadingDots size="sm" color="bg-gray-500" />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Scroll anchor */}
                    <div ref={messagesEndRef} />
                  </>
                )}
              </div>
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-200 bg-white p-4">
              <div className="max-w-3xl mx-auto">
                <div className="flex items-end space-x-3">
                  <div className="flex-1 relative">
                    <textarea
                      value={step === 'conversation' ? treatmentInput : chatInput}
                      onChange={(e) => {
                        if (step === 'conversation') {
                          setTreatmentInput(e.target.value);
                        } else {
                          setChatInput(e.target.value);
                        }
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (step === 'conversation') {
                            sendTreatmentMessage();
                          } else {
                            sendChatMessage();
                          }
                        }
                      }}
                      placeholder="Type your message..."
                      className="w-full resize-none border border-gray-300 rounded-xl px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent max-h-32 chat-input"
                      rows={1}
                      style={{ minHeight: '48px' }}
                    />
                  </div>
                  <button
                    onClick={step === 'conversation' ? sendTreatmentMessage : sendChatMessage}
                    disabled={
                      loading ||
                      (step === 'conversation' ? !treatmentInput.trim() : !chatInput.trim())
                    }
                    className="w-10 h-10 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-xl flex items-center justify-center transition-colors chat-button"
                  >
                    {loading ? (
                      <LoadingDots size="sm" color="bg-white" />
                    ) : (
                      <PaperAirplaneIcon className="w-5 h-5 text-white" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Press Enter to send, Shift+Enter for new line
                </p>
              </div>
            </div>
          </>
        )}

        </div>
      </div>
    </div>
  );
}
