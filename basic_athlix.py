#!/usr/bin/env python3
"""
Basic ATHLIX application with core functionality
This version focuses on demonstrating the key features without complex authentication
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, EmailStr
from typing import List, Dict, Optional, AsyncGenerator
import uuid
import json
import asyncio
from datetime import datetime
import uvicorn

# Simple in-memory storage for demo
users_db: Dict[str, dict] = {}
chat_sessions: Dict[str, List[dict]] = {}
treatment_sessions: Dict[str, dict] = {}

# Pydantic models
class UserCreate(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: str
    first_name: str
    last_name: str
    email: str
    created_at: str

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class ChatMessage(BaseModel):
    session_id: Optional[str] = None
    message: str
    message_type: str = "text"

class ChatResponse(BaseModel):
    session_id: str
    response: str
    timestamp: str

class TreatmentQuestion(BaseModel):
    session_id: str
    question_id: str
    answer: str

# Initialize FastAPI app
app = FastAPI(
    title="ATHLIX - AI Treatment Assistant",
    description="Basic version with core functionality",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to ATHLIX - AI Treatment Assistant",
        "version": "1.0.0",
        "description": "Basic version demonstrating core functionality",
        "status": "running",
        "features": [
            "User registration and login",
            "Chat sessions",
            "Treatment questionnaires",
            "Health monitoring",
            "API documentation"
        ],
        "endpoints": {
            "Authentication": {
                "POST /register": "Register a new user",
                "POST /login": "Login user",
                "GET /users": "List all users"
            },
            "Chat": {
                "POST /chat/new": "Create new chat session",
                "POST /chat/message": "Send chat message",
                "GET /chat/sessions": "Get user chat sessions",
                "GET /chat/{session_id}/history": "Get chat history"
            },
            "Treatment": {
                "POST /treatment/start": "Start treatment session",
                "POST /treatment/answer": "Answer treatment question",
                "GET /treatment/{session_id}": "Get treatment status"
            },
            "System": {
                "GET /health": "Health check",
                "GET /docs": "API documentation"
            }
        },
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app": "ATHLIX",
        "version": "1.0.0",
        "users_count": len(users_db),
        "active_chats": len(chat_sessions),
        "treatment_sessions": len(treatment_sessions),
        "timestamp": datetime.now().isoformat()
    }

@app.post("/register", response_model=UserResponse)
async def register_user(user: UserCreate):
    """Register a new user"""
    # Check if user already exists
    for existing_user in users_db.values():
        if existing_user["email"] == user.email:
            raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    user_id = str(uuid.uuid4())
    user_data = {
        "id": user_id,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "email": user.email,
        "password": user.password,  # In production, this should be hashed
        "created_at": datetime.now().isoformat(),
        "is_active": True
    }
    
    users_db[user_id] = user_data
    
    return UserResponse(
        id=user_id,
        first_name=user.first_name,
        last_name=user.last_name,
        email=user.email,
        created_at=user_data["created_at"]
    )

@app.post("/login")
async def login_user(login: LoginRequest):
    """Login user"""
    # Find user by email
    user = None
    for user_data in users_db.values():
        if user_data["email"] == login.email and user_data["password"] == login.password:
            user = user_data
            break
    
    if not user:
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    return {
        "message": "Login successful",
        "user_id": user["id"],
        "access_token": f"token_{user['id']}",  # Simple token for demo
        "user": {
            "id": user["id"],
            "first_name": user["first_name"],
            "last_name": user["last_name"],
            "email": user["email"]
        }
    }

@app.get("/users")
async def list_users():
    """List all registered users"""
    return {
        "users": [
            {
                "id": user["id"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "email": user["email"],
                "created_at": user["created_at"]
            }
            for user in users_db.values()
        ],
        "total": len(users_db)
    }

@app.post("/chat/new")
async def create_chat_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())
    chat_sessions[session_id] = []
    
    return {
        "session_id": session_id,
        "message": "Chat session created successfully",
        "created_at": datetime.now().isoformat()
    }

@app.post("/chat/message", response_model=ChatResponse)
async def send_chat_message(chat_msg: ChatMessage):
    """Send a message to chat session"""
    if not chat_msg.session_id:
        # Create new session if none provided
        chat_msg.session_id = str(uuid.uuid4())
        chat_sessions[chat_msg.session_id] = []
    
    if chat_msg.session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Add user message to history
    user_message = {
        "role": "user",
        "content": chat_msg.message,
        "timestamp": datetime.now().isoformat()
    }
    chat_sessions[chat_msg.session_id].append(user_message)
    
    # Generate AI response (mock response for demo)
    ai_response = f"Thank you for your message: '{chat_msg.message}'. As ATHLIX, I'm here to help with your health and treatment questions. How can I assist you further?"
    
    ai_message = {
        "role": "assistant",
        "content": ai_response,
        "timestamp": datetime.now().isoformat()
    }
    chat_sessions[chat_msg.session_id].append(ai_message)
    
    return ChatResponse(
        session_id=chat_msg.session_id,
        response=ai_response,
        timestamp=ai_message["timestamp"]
    )

@app.get("/chat/sessions")
async def get_chat_sessions():
    """Get all chat sessions"""
    return {
        "sessions": [
            {
                "session_id": session_id,
                "message_count": len(messages),
                "last_activity": messages[-1]["timestamp"] if messages else None
            }
            for session_id, messages in chat_sessions.items()
        ],
        "total": len(chat_sessions)
    }

@app.get("/chat/{session_id}/history")
async def get_chat_history(session_id: str):
    """Get chat history for a session"""
    if session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Chat session not found")

    return {
        "session_id": session_id,
        "messages": chat_sessions[session_id],
        "message_count": len(chat_sessions[session_id])
    }

@app.delete("/session/{session_id}")
async def delete_chat_session(session_id: str):
    """Delete a chat session"""
    if session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # Remove the session from memory
    del chat_sessions[session_id]

    # Also remove from treatment sessions if it exists
    if session_id in treatment_sessions:
        del treatment_sessions[session_id]

    return {"message": "Chat session deleted successfully"}

@app.post("/treatment/start")
async def start_treatment_session():
    """Start a new treatment session"""
    session_id = str(uuid.uuid4())
    
    # Mock treatment questions
    questions = [
        {
            "id": "q1",
            "question": "Can you describe the pain or discomfort you're experiencing?",
            "type": "text"
        },
        {
            "id": "q2",
            "question": "On a scale of 1-10, how would you rate your pain level?",
            "type": "number"
        },
        {
            "id": "q3",
            "question": "How long have you been experiencing this issue?",
            "type": "multiple_choice",
            "options": ["Less than a week", "1-2 weeks", "1 month", "Several months", "Over a year"]
        }
    ]
    
    treatment_sessions[session_id] = {
        "session_id": session_id,
        "questions": questions,
        "answers": [],
        "current_question_index": 0,
        "status": "questioning",
        "created_at": datetime.now().isoformat()
    }
    
    return {
        "session_id": session_id,
        "status": "questioning",
        "current_question": questions[0],
        "total_questions": len(questions)
    }

@app.post("/treatment/answer")
async def answer_treatment_question(answer: TreatmentQuestion):
    """Answer a treatment question"""
    if answer.session_id not in treatment_sessions:
        raise HTTPException(status_code=404, detail="Treatment session not found")
    
    session = treatment_sessions[answer.session_id]
    
    # Add answer
    session["answers"].append({
        "question_id": answer.question_id,
        "answer": answer.answer,
        "timestamp": datetime.now().isoformat()
    })
    
    session["current_question_index"] += 1
    
    # Check if all questions answered
    if session["current_question_index"] >= len(session["questions"]):
        session["status"] = "completed"
        session["treatment"] = "Based on your responses, here are some general recommendations: Rest, apply ice if there's swelling, and consider consulting a healthcare professional if symptoms persist."
        
        return {
            "session_id": answer.session_id,
            "status": "completed",
            "treatment": session["treatment"]
        }
    else:
        next_question = session["questions"][session["current_question_index"]]
        return {
            "session_id": answer.session_id,
            "status": "questioning",
            "current_question": next_question,
            "questions_remaining": len(session["questions"]) - session["current_question_index"]
        }

@app.get("/treatment/{session_id}")
async def get_treatment_session(session_id: str):
    """Get treatment session status"""
    if session_id not in treatment_sessions:
        raise HTTPException(status_code=404, detail="Treatment session not found")
    
    return treatment_sessions[session_id]

if __name__ == "__main__":
    print("🚀 Starting ATHLIX Basic Application...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("💡 This is a basic version demonstrating core functionality")
    print("🧪 Test with: python test_athlix_backend.py")
    uvicorn.run(
        "basic_athlix:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
